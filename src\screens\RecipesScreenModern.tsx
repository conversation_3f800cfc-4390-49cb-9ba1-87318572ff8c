import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
  Dimensions,
  StatusBar,
  FlatList,
  ImageBackground,
  Pressable,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Animated, {
  FadeInUp,
  FadeInDown,
  FadeInLeft,
  FadeInRight,
  SlideInRight,
  SlideInLeft,
  ZoomIn,
  BounceIn,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
  withSequence,
  withDelay,
  withRepeat,
} from 'react-native-reanimated';
import { useNavigation } from '@react-navigation/native';


import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows, BlurIntensity } from '../constants/Colors';
import { AnimationConfig } from '../utils/AnimationUtils';
import { ModernCard } from '../components/ModernCard';
import { ModernButton } from '../components/ModernButton';
import { ModernInput } from '../components/ModernInput';
import { ModernModal } from '../components/ModernModal';
import { ModernLoading } from '../components/ModernLoading';
import LottieIcon from '../components/LottieIcon';
import { CircularProgress } from '../components/CircularProgress';
import * as Haptics from 'expo-haptics';
import ApiService from '../services/ApiService';
import ErrorHandlingService from '../services/ErrorHandlingService';
import RecipeCacheService from '../services/RecipeCacheService';
import VoiceService, { VoiceServiceCallbacks } from '../services/VoiceService';
import FavoritesService from '../services/FavoritesService';

const { width, height } = Dimensions.get('window');

// Premium Favorite Card Component
const PremiumFavoriteCard: React.FC<{
  favorite: any;
  index: number;
  favoriteImages: {[key: string]: string};
  onPress: () => void;
  onRemove: () => void;
}> = ({ favorite, index, favoriteImages, onPress, onRemove }) => {
  const scale = useSharedValue(1);
  const translateY = useSharedValue(0);
  const glowOpacity = useSharedValue(0);
  const heartScale = useSharedValue(1);

  const handlePressIn = () => {
    scale.value = withSpring(0.95, { damping: 15, stiffness: 300 });
    translateY.value = withSpring(-2, { damping: 15, stiffness: 300 });
    glowOpacity.value = withTiming(1, { duration: 200 });
  };

  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15, stiffness: 300 });
    translateY.value = withSpring(0, { damping: 15, stiffness: 300 });
    glowOpacity.value = withTiming(0, { duration: 300 });
  };

  const handleHeartPress = () => {
    heartScale.value = withSequence(
      withTiming(1.3, { duration: 150 }),
      withTiming(1, { duration: 150 })
    );
    runOnJS(() => {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      onRemove();
    })();
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scale.value },
      { translateY: translateY.value },
    ],
  }));

  const glowStyle = useAnimatedStyle(() => ({
    opacity: glowOpacity.value,
  }));

  const heartAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: heartScale.value }],
  }));

  return (
    <Animated.View
      entering={SlideInRight.delay(index * 150).duration(600)}
      style={[styles.premiumFavoriteCard, animatedStyle]}
    >
      {/* Glow Effect */}
      <Animated.View style={[styles.premiumFavoriteGlow, glowStyle]} />

      <TouchableOpacity
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        style={styles.premiumFavoriteCardButton}
        activeOpacity={1}
      >
        <View style={styles.premiumFavoriteImageContainer}>
          <ImageBackground
            source={{
              uri: favoriteImages[favorite.name] || favorite.imageUrl || 'https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=600&h=400&fit=crop&crop=center&q=80'
            }}
            style={styles.premiumFavoriteImage}
            imageStyle={styles.premiumFavoriteImageStyle}
          >
            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.3)']}
              style={styles.premiumFavoriteImageGradient}
            />

            {/* Heart Button */}
            <TouchableOpacity
              style={styles.premiumFavoriteHeartButton}
              onPress={handleHeartPress}
            >
              <Animated.View style={heartAnimatedStyle}>
                <Ionicons name="heart" size={18} color="#FF6B6B" />
              </Animated.View>
            </TouchableOpacity>

            {/* Premium Badge */}
            <View style={styles.premiumFavoriteBadge}>
              <Ionicons name="star" size={12} color="#FFD700" />
            </View>
          </ImageBackground>
        </View>

        <View style={styles.premiumFavoriteContent}>
          <Text style={styles.premiumFavoriteName} numberOfLines={2}>
            {favorite.name}
          </Text>

          <View style={styles.premiumFavoriteMeta}>
            {favorite.cookingTime && (
              <View style={styles.premiumFavoriteMetaItem}>
                <Ionicons name="time-outline" size={14} color="#8B9A7A" />
                <Text style={styles.premiumFavoriteMetaText}>{favorite.cookingTime}</Text>
              </View>
            )}
            {favorite.calories && (
              <View style={styles.premiumFavoriteMetaItem}>
                <Ionicons name="flame-outline" size={14} color="#FF6B6B" />
                <Text style={styles.premiumFavoriteMetaText}>{favorite.calories} cal</Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

interface Recipe {
  id: string;
  title: string;
  description: string;
  cookTime: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  calories: number;
  ingredients: string[];
  instructions: string[];
  tags: string[];
  image?: string;
  imageUrl?: string; // Unsplash image URL
  rating?: number;
  servings?: number;
  prepTime?: string;
  nutrition?: {
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
  };
}

interface FilterChipProps {
  label: string;
  icon?: keyof typeof Ionicons.glyphMap;
  selected: boolean;
  onPress: () => void;
  variant?: 'default' | 'dietary' | 'difficulty' | 'time';
}

interface RecipeCardProps {
  recipe: Recipe;
  onPress: () => void;
  index: number;
  variant?: 'default' | 'featured' | 'compact';
}

interface SearchHeaderProps {
  searchQuery: string;
  onSearchChange: (text: string) => void;
  onVoicePress: () => void;
  onFilterPress: () => void;
  isListening: boolean;
  voiceText: string;
}



// Modern Search Header Component
const SearchHeader: React.FC<SearchHeaderProps> = ({
  searchQuery,
  onSearchChange,
  onVoicePress,
  onFilterPress,
  isListening,
  voiceText,
}) => {
  const voiceScale = useSharedValue(1);
  const voiceOpacity = useSharedValue(1);

  useEffect(() => {
    if (isListening) {
      voiceScale.value = withSequence(
        withTiming(1.2, { duration: 300 }),
        withTiming(1, { duration: 300 })
      );
      voiceOpacity.value = withSequence(
        withTiming(0.7, { duration: 500 }),
        withTiming(1, { duration: 500 })
      );
    }
  }, [isListening]);

  const voiceAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: voiceScale.value }],
    opacity: voiceOpacity.value,
  }));

  return (
    <Animated.View entering={FadeInDown.duration(600)} style={styles.searchHeader}>
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <TextInput
            value={searchQuery}
            onChangeText={onSearchChange}
            placeholder="Search delicious recipes..."
            placeholderTextColor={Colors.mutedForeground}
            style={styles.searchInput}
          />
          <Animated.View style={voiceAnimatedStyle}>
            <TouchableOpacity
              style={[styles.voiceButton, isListening && styles.voiceButtonActive]}
              onPress={onVoicePress}
            >
              <Ionicons
                name={isListening ? "mic" : "mic-outline"}
                size={18}
                color={isListening ? "#FFFFFF" : Colors.brand}
              />
            </TouchableOpacity>
          </Animated.View>
        </View>
        {voiceText && (
          <Animated.Text entering={FadeInUp.duration(300)} style={styles.voiceText}>
            {voiceText}
          </Animated.Text>
        )}
      </View>

      <TouchableOpacity style={styles.filterButton} onPress={onFilterPress}>
        <Ionicons name="options" size={20} color="#6B7C5A" />
      </TouchableOpacity>
    </Animated.View>
  );
};

// Filter Chip Component
const FilterChip: React.FC<FilterChipProps> = ({
  label,
  icon,
  selected,
  onPress,
  variant = 'default'
}) => {
  const scale = useSharedValue(1);
  const backgroundColor = useSharedValue(selected ? 1 : 0);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handlePress = () => {
    scale.value = withSequence(
      withTiming(0.95, { duration: 100 }),
      withTiming(1, { duration: 100 })
    );
    backgroundColor.value = withTiming(selected ? 0 : 1, { duration: 200 });
    onPress();
  };

  return (
    <Animated.View style={[styles.filterChip, animatedStyle]}>
      <TouchableOpacity
        style={[
          styles.filterChipButton,
          selected && styles.filterChipButtonSelected
        ]}
        onPress={handlePress}
      >
        {icon && (
          <Ionicons
            name={icon}
            size={16}
            color={selected ? "#FFFFFF" : "#6B7C5A"}
            style={styles.filterChipIcon}
          />
        )}
        <Text style={[
          styles.filterChipText,
          selected && styles.filterChipTextSelected
        ]}>
          {label}
        </Text>
      </TouchableOpacity>
    </Animated.View>
  );
};

// Recipe Card Component
const RecipeCard: React.FC<RecipeCardProps> = ({
  recipe,
  onPress,
  index,
  variant = 'default'
}) => {
  const scale = useSharedValue(1);
  const shadowOpacity = useSharedValue(0.1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const shadowAnimatedStyle = useAnimatedStyle(() => ({
    shadowOpacity: shadowOpacity.value,
  }));

  const handlePressIn = () => {
    scale.value = withSpring(0.98, { damping: 15, stiffness: 400 });
    shadowOpacity.value = withTiming(0.2, { duration: 150 });
  };

  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15, stiffness: 400 });
    shadowOpacity.value = withTiming(0.1, { duration: 150 });
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return '#4CAF50';
      case 'medium': return '#FF9800';
      case 'hard': return '#F44336';
      default: return '#6B7C5A';
    }
  };

  const getDifficultyIcon = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return 'checkmark-circle';
      case 'medium': return 'time';
      case 'hard': return 'flame';
      default: return 'help-circle';
    }
  };

  if (variant === 'featured') {
    return (
      <Animated.View
        entering={FadeInUp.delay(index * 100).duration(600)}
        style={[styles.featuredCard, animatedStyle, shadowAnimatedStyle]}
      >
        <TouchableOpacity
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          style={styles.featuredCardButton}
          activeOpacity={1}
        >
          <ImageBackground
            source={{ uri: recipe.imageUrl || recipe.image || 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80' }}
            style={styles.featuredCardImage}
            imageStyle={styles.featuredCardImageStyle}
          >
            <View style={styles.featuredCardOverlay}>
              <View style={styles.featuredCardContent}>
                <View style={styles.featuredCardBadges}>
                  <View style={[styles.difficultyBadge, { backgroundColor: getDifficultyColor(recipe.difficulty) }]}>
                    <Ionicons name={getDifficultyIcon(recipe.difficulty) as any} size={12} color="#FFFFFF" />
                    <Text style={styles.difficultyBadgeText}>{recipe.difficulty}</Text>
                  </View>
                  <View style={styles.timeBadge}>
                    <Ionicons name="time" size={12} color="#FFFFFF" />
                    <Text style={styles.timeBadgeText}>{recipe.cookTime}</Text>
                  </View>
                  {recipe.rating && (
                    <View style={styles.ratingBadge}>
                      <Ionicons name="star" size={12} color="#FFD700" />
                      <Text style={styles.ratingBadgeText}>{recipe.rating}</Text>
                    </View>
                  )}
                </View>

                <View style={styles.featuredCardInfo}>
                  <Text style={styles.featuredCardTitle}>{recipe.title}</Text>
                  <Text style={styles.featuredCardDescription} numberOfLines={2}>{recipe.description}</Text>

                  <View style={styles.featuredCardStats}>
                    <View style={styles.statItem}>
                      <Ionicons name="flame" size={16} color="#FF6B6B" />
                      <Text style={styles.statText}>{recipe.calories} cal</Text>
                    </View>
                    {recipe.rating && (
                      <View style={styles.statItem}>
                        <Ionicons name="star" size={16} color={Colors.warning} />
                        <Text style={styles.statText}>{recipe.rating}</Text>
                      </View>
                    )}
                  </View>
                </View>
              </View>
            </View>
          </ImageBackground>
        </TouchableOpacity>
      </Animated.View>
    );
  }

  return (
    <Animated.View
      entering={FadeInUp.delay(index * 100).duration(600)}
      style={[styles.recipeCard, animatedStyle, shadowAnimatedStyle]}
    >
      <TouchableOpacity
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        style={styles.recipeCardButton}
        activeOpacity={1}
      >
        <View style={styles.recipeCardImage}>
          <ImageBackground
            source={{ uri: recipe.imageUrl || recipe.image || 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80' }}
            style={styles.cardImageBackground}
            imageStyle={styles.cardImageStyle}
          >
            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.4)']}
              style={styles.cardImageOverlay}
            >
              <View style={styles.cardBadges}>
                <View style={[styles.difficultyBadge, { backgroundColor: getDifficultyColor(recipe.difficulty) }]}>
                  <Ionicons name={getDifficultyIcon(recipe.difficulty) as any} size={10} color="#FFFFFF" />
                  <Text style={styles.difficultyBadgeText}>{recipe.difficulty}</Text>
                </View>
                {recipe.rating && (
                  <View style={styles.ratingBadge}>
                    <Ionicons name="star" size={10} color="#FFD700" />
                    <Text style={styles.ratingBadgeText}>{recipe.rating}</Text>
                  </View>
                )}
              </View>
            </LinearGradient>
          </ImageBackground>
        </View>

        <View style={styles.recipeCardContent}>
          <Text style={styles.recipeCardTitle} numberOfLines={2}>{recipe.title}</Text>
          <Text style={styles.recipeCardDescription} numberOfLines={2}>
            {recipe.description}
          </Text>

          <View style={styles.recipeCardStats}>
            <View style={styles.statItem}>
              <LottieIcon name="timer" size={14} color={Colors.brand} enableHaptics={false} />
              <Text style={styles.statText}>{recipe.cookTime}</Text>
            </View>
            <View style={styles.statItem}>
              <LottieIcon name="fire" size={14} color="#FF6B35" enableHaptics={false} />
              <Text style={styles.statText}>{recipe.calories} cal</Text>
            </View>
            {recipe.servings && (
              <View style={styles.statItem}>
                <LottieIcon name="chef" size={14} color="#4ECDC4" enableHaptics={false} />
                <Text style={styles.statText}>{recipe.servings}</Text>
              </View>
            )}
          </View>

          {/* Ingredient Preview */}
          {recipe.ingredients && recipe.ingredients.length > 0 && (
            <View style={styles.ingredientPreview}>
              <Text style={styles.ingredientPreviewText}>
                {recipe.ingredients.slice(0, 3).join(', ')}
                {recipe.ingredients.length > 3 && '...'}
              </Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const RecipesScreenModern: React.FC = () => {
  const navigation = useNavigation();
  const [searchQuery, setSearchQuery] = useState('');
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedDietary, setSelectedDietary] = useState<string[]>([]);
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('');
  const [maxCookTime, setMaxCookTime] = useState<string>('');
  const [availableIngredients, setAvailableIngredients] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'relevance' | 'time' | 'calories' | 'rating'>('relevance');

  // Voice recognition states
  const [isListening, setIsListening] = useState(false);
  const [voiceText, setVoiceText] = useState('');

  // Favorites states
  const [favoriteRecipes, setFavoriteRecipes] = useState<any[]>([]);
  const [favoritesLoading, setFavoritesLoading] = useState(false);
  const [showAllFavorites, setShowAllFavorites] = useState(false);
  const [allFavorites, setAllFavorites] = useState<any[]>([]);
  const [favoritesPage, setFavoritesPage] = useState(1);
  const [hasMoreFavorites, setHasMoreFavorites] = useState(true);
  const [loadingMoreFavorites, setLoadingMoreFavorites] = useState(false);
  const [favoriteImages, setFavoriteImages] = useState<{[key: string]: string}>({});

  const dietaryOptions = [
    { id: 'vegetarian', label: 'Vegetarian', icon: 'leaf' },
    { id: 'vegan', label: 'Vegan', icon: 'flower' },
    { id: 'gluten-free', label: 'Gluten-Free', icon: 'shield-checkmark' },
    { id: 'keto', label: 'Keto', icon: 'flame' },
    { id: 'low-carb', label: 'Low Carb', icon: 'trending-down' },
    { id: 'high-protein', label: 'High Protein', icon: 'fitness' },
  ];

  const difficultyOptions = [
    { id: 'easy', label: 'Easy', icon: 'happy' },
    { id: 'medium', label: 'Medium', icon: 'star' },
    { id: 'hard', label: 'Hard', icon: 'flame' },
  ];

  const timeOptions = [
    { id: '15', label: '15 min' },
    { id: '30', label: '30 min' },
    { id: '45', label: '45 min' },
    { id: '60', label: '1 hour' },
  ];

  // Animation values
  const voiceScale = useSharedValue(1);
  const voiceOpacity = useSharedValue(1);

  const voiceAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: voiceScale.value }],
    opacity: voiceOpacity.value,
  }));

  useEffect(() => {
    if (isListening) {
      voiceScale.value = withSequence(
        withTiming(1.2, { duration: 300 }),
        withTiming(1, { duration: 300 })
      );
      voiceOpacity.value = withSequence(
        withTiming(0.7, { duration: 500 }),
        withTiming(1, { duration: 500 })
      );
    }
  }, [isListening]);

  // Voice recognition handlers
  const startVoiceRecognition = async () => {
    try {
      const isInitialized = await VoiceService.initialize();
      if (!isInitialized) {
        Alert.alert(
          'Voice Recognition Unavailable',
          'Voice recognition is not available in this environment. Please:\n\n1. Open in web browser (press "w" in Expo CLI)\n2. Use Chrome, Safari, or Edge\n3. Allow microphone permissions\n\nOr build the app with EAS Build for native voice support.',
          [{ text: 'OK' }]
        );
        return;
      }

      const callbacks: VoiceServiceCallbacks = {
        onStart: () => {
          setIsListening(true);
          setVoiceText('Listening...');
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        },
        onResult: (result) => {
          if (result.isFinal && result.text.trim()) {
            setVoiceText('');
            setIsListening(false);
            setSearchQuery(result.text);
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          }
        },
        onPartialResult: (text) => {
          setVoiceText(text);
        },
        onError: (error) => {
          setIsListening(false);
          setVoiceText('');
          Alert.alert('Voice Recognition Error', error);
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
        },
        onEnd: () => {
          setIsListening(false);
        },
      };

      const started = await VoiceService.startListening(callbacks);
      if (!started) {
        setIsListening(false);
      }
    } catch (error) {
      console.error('Error starting voice recognition:', error);
      setIsListening(false);
      Alert.alert('Error', 'Failed to start voice recognition');
    }
  };

  const stopVoiceRecognition = async () => {
    try {
      await VoiceService.stopListening();
      setIsListening(false);
      setVoiceText('');
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } catch (error) {
      console.error('Error stopping voice recognition:', error);
      setIsListening(false);
      setVoiceText('');
    }
  };

  // Load cached images for favorites from permanent image cache
  const loadCachedImagesForFavorites = async (favorites: any[]) => {
    try {
      const imageCache: {[key: string]: string} = {};

      // Get the permanent image cache from AsyncStorage
      const cacheJson = await AsyncStorage.getItem('permanent_meal_image_cache');
      if (cacheJson) {
        const cache = JSON.parse(cacheJson);

        // Map favorite names to cached images
        favorites.forEach(favorite => {
          const normalizedName = favorite.name.toLowerCase().trim();
          if (cache[normalizedName]) {
            imageCache[favorite.name] = cache[normalizedName].imageUrl;
            console.log(`📖 Found cached image for favorite: ${favorite.name}`);
          }
        });
      }

      setFavoriteImages(imageCache);
      console.log(`🖼️ Loaded cached images for ${Object.keys(imageCache).length} favorites`);
    } catch (error) {
      console.error('❌ Error loading cached images for favorites:', error);
    }
  };

  // Load favorite recipes with cached images
  const loadFavorites = async () => {
    try {
      setFavoritesLoading(true);
      const favorites = await FavoritesService.getFavorites(1, 6); // Load first 6 favorites
      setFavoriteRecipes(favorites);

      // Load cached images for these favorites
      await loadCachedImagesForFavorites(favorites);

      console.log(`📚 Loaded ${favorites.length} favorite recipes with cached images`);
    } catch (error) {
      console.error('❌ Error loading favorites:', error);
    } finally {
      setFavoritesLoading(false);
    }
  };

  // Load all favorites with pagination and cached images
  const loadAllFavorites = async (page: number = 1, reset: boolean = false) => {
    try {
      if (page === 1) setFavoritesLoading(true);
      else setLoadingMoreFavorites(true);

      const pageSize = 20;
      const favorites = await FavoritesService.getFavorites(page, pageSize);

      if (reset || page === 1) {
        setAllFavorites(favorites);
        // Load cached images for new favorites
        await loadCachedImagesForFavorites(favorites);
      } else {
        setAllFavorites(prev => {
          const newFavorites = [...prev, ...favorites];
          // Load cached images for additional favorites
          loadCachedImagesForFavorites(favorites);
          return newFavorites;
        });
      }

      setHasMoreFavorites(favorites.length === pageSize);
      setFavoritesPage(page);

      console.log(`📚 Loaded page ${page} of favorites: ${favorites.length} items with cached images`);
    } catch (error) {
      console.error('❌ Error loading all favorites:', error);
    } finally {
      setFavoritesLoading(false);
      setLoadingMoreFavorites(false);
    }
  };

  // Load more favorites (pagination)
  const loadMoreFavorites = () => {
    if (!loadingMoreFavorites && hasMoreFavorites) {
      loadAllFavorites(favoritesPage + 1, false);
    }
  };

  // Load favorites on component mount and when screen is focused
  useEffect(() => {
    loadFavorites();
  }, []);

  // Add focus listener to refresh favorites when returning to screen
  useFocusEffect(
    React.useCallback(() => {
      loadFavorites(); // Refresh favorites when screen comes into focus
    }, [])
  );

  // Load all favorites when modal opens
  useEffect(() => {
    if (showAllFavorites) {
      loadAllFavorites(1, true);
    }
  }, [showAllFavorites]);

  const generateRecipes = async () => {
    // Check network connectivity first
    const isConnected = await ErrorHandlingService.checkNetworkConnectivity();
    if (!isConnected) {
      Alert.alert(
        'No Internet Connection',
        'Please check your internet connection and try again.',
        [{ text: 'OK', style: 'default' }]
      );
      return;
    }

    setLoading(true);
    try {
      // Build enhanced query with filters
      let query = searchQuery || 'healthy meal';

      if (selectedDietary.length > 0) {
        query += ` that is ${selectedDietary.join(' and ')}`;
      }

      if (selectedDifficulty) {
        query += ` with ${selectedDifficulty} difficulty level`;
      }

      if (maxCookTime) {
        query += ` that takes maximum ${maxCookTime} minutes to cook`;
      }

      if (availableIngredients.length > 0) {
        query += ` using ingredients like ${availableIngredients.join(', ')}`;
      }

      // Use RecipeCacheService to get or generate recipe
      const cachedRecipe = await RecipeCacheService.getOrGenerateRecipe(query, 'generated');

      // Convert cached recipe to display format
      const recipeWithId = {
        id: cachedRecipe.id,
        title: cachedRecipe.title,
        description: cachedRecipe.description,
        cookTime: cachedRecipe.cookTime,
        difficulty: cachedRecipe.difficulty,
        calories: cachedRecipe.nutrition.calories,
        ingredients: cachedRecipe.ingredients,
        instructions: cachedRecipe.instructions,
        tags: cachedRecipe.tags,
        image: cachedRecipe.imageUrl,
        imageUrl: cachedRecipe.imageUrl
      };

      setRecipes([recipeWithId]);
    } catch (error) {
      console.error('Error generating recipes:', error);

      // Try to get cached recipes as fallback instead of static data
      try {
        console.log('🔄 Attempting to get cached recipes as fallback...');
        const fallbackQueries = ['healthy meal', 'quick dinner', 'nutritious lunch'];
        const fallbackRecipes = [];

        for (const fallbackQuery of fallbackQueries) {
          try {
            const cachedRecipe = await RecipeCacheService.getOrGenerateRecipe(fallbackQuery, 'generated');
            fallbackRecipes.push({
              id: cachedRecipe.id,
              title: cachedRecipe.title,
              description: cachedRecipe.description,
              cookTime: cachedRecipe.cookTime,
              difficulty: cachedRecipe.difficulty,
              calories: cachedRecipe.nutrition.calories,
              ingredients: cachedRecipe.ingredients,
              instructions: cachedRecipe.instructions,
              tags: cachedRecipe.tags,
              image: cachedRecipe.imageUrl,
              imageUrl: cachedRecipe.imageUrl
            });
          } catch (fallbackError) {
            console.warn(`⚠️ Failed to get fallback recipe for ${fallbackQuery}:`, fallbackError);
          }
        }

        if (fallbackRecipes.length > 0) {
          console.log(`✅ Using ${fallbackRecipes.length} cached recipes as fallback`);
          setRecipes(fallbackRecipes);
        } else {
          console.log('⚠️ No cached recipes available, showing empty state');
          setRecipes([]);
        }
      } catch (fallbackError) {
        console.error('❌ Fallback recipe retrieval failed:', fallbackError);
        setRecipes([]);
      }
    }
    setLoading(false);
  };

  const toggleDietary = (option: string) => {
    setSelectedDietary(prev =>
      prev.includes(option)
        ? prev.filter(item => item !== option)
        : [...prev, option]
    );
  };

  const searchRecipes = () => {
    generateRecipes();
  };

  const getRandomRecipe = () => {
    setSearchQuery('surprise me with a random recipe');
    generateRecipes();
  };

  // Helper functions for recipe cards
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return '#4CAF50';
      case 'medium': return '#FF9800';
      case 'hard': return '#F44336';
      default: return '#6B7C5A';
    }
  };

  const getDifficultyIcon = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return 'checkmark-circle';
      case 'medium': return 'time';
      case 'hard': return 'flame';
      default: return 'help-circle';
    }
  };

  const clearFilters = () => {
    setSelectedDietary([]);
    setSelectedDifficulty('');
    setMaxCookTime('');
    setAvailableIngredients([]);
  };

  const toggleDietaryFilter = (option: string) => {
    toggleDietary(option);
  };

  const DietaryChip: React.FC<{ option: any; isSelected: boolean; onPress: () => void }> = ({
    option,
    isSelected,
    onPress
  }) => (
    <TouchableOpacity
      style={[styles.dietaryChip, isSelected && styles.dietaryChipSelected]}
      onPress={onPress}
    >
      <Ionicons
        name={option.icon as any}
        size={16}
        color={isSelected ? Colors.brandForeground : Colors.brand}
      />
      <Text style={[
        styles.dietaryChipText,
        isSelected && styles.dietaryChipTextSelected
      ]}>
        {option.label}
      </Text>
    </TouchableOpacity>
  );

  const RecipeCard: React.FC<{ recipe: Recipe; delay: number }> = ({ recipe, delay }) => {
    const cardNavigation = useNavigation();

    return (
      <Animated.View entering={FadeInUp.delay(delay).duration(600)}>
        <TouchableOpacity style={styles.recipeCard}>
          <View style={styles.recipeHeader}>
            <View style={styles.recipeInfo}>
              <Text style={styles.recipeTitle}>{recipe.title}</Text>
              <Text style={styles.recipeDescription}>{recipe.description}</Text>
            </View>
            <View style={styles.recipeStats}>
              <View style={styles.statItem}>
                <Ionicons name="time" size={14} color={Colors.mutedForeground} />
                <Text style={styles.statText}>{recipe.cookTime}</Text>
              </View>
              <View style={styles.statItem}>
                <Ionicons name="flame" size={14} color={Colors.mutedForeground} />
                <Text style={styles.statText}>{recipe.calories} cal</Text>
              </View>
            </View>
          </View>

          <View style={styles.recipeTags}>
            {recipe.tags.map((tag, index) => (
              <View key={index} style={styles.tag}>
                <Text style={styles.tagText}>{tag}</Text>
              </View>
            ))}
            <View style={[styles.tag, styles.difficultyTag]}>
              <Text style={styles.difficultyText}>{recipe.difficulty}</Text>
            </View>
          </View>

          <View style={styles.recipeFooter}>
            <Text style={styles.ingredientsLabel}>
              {recipe.ingredients.length} ingredients
            </Text>
            <TouchableOpacity
              style={styles.viewButton}
              onPress={() => (cardNavigation as any).navigate('RecipeDetail', { recipe })}
            >
              <Text style={styles.viewButtonText}>View Recipe</Text>
              <Ionicons name="chevron-forward" size={16} color={Colors.brand} />
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const featuredRecipes = recipes.slice(0, 3);
  const regularRecipes = recipes.slice(3);

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />

      {/* Beautiful Background with New Screens Background Image */}
      <ImageBackground
        source={require('../../assets/screens background.jpg')}
        style={styles.backgroundContainer}
        resizeMode="cover"
      >
        <View style={styles.whiteOverlay} />
      </ImageBackground>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Modern Search Header */}
        <View style={styles.modernSearchHeader}>
          <Animated.View entering={FadeInDown.duration(600)} style={styles.searchSection}>
            <Text style={styles.headerTitle}>Discover Recipes</Text>
            <Text style={styles.headerSubtitle}>Find your perfect meal</Text>

            <View style={styles.modernSearchContainer}>
              <View style={styles.searchInputWrapper}>
                <TextInput
                  value={searchQuery}
                  onChangeText={(text) => {
                    setSearchQuery(text);
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }}
                  placeholder="Search delicious recipes..."
                  placeholderTextColor={Colors.mutedForeground}
                  style={styles.modernSearchInput}
                />
                <TouchableOpacity
                  style={styles.searchButton}
                  onPress={() => {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                    searchRecipes();
                  }}
                >
                  <Ionicons
                    name="search"
                    size={18}
                    color="#FFFFFF"
                  />
                </TouchableOpacity>

                <Animated.View style={voiceAnimatedStyle}>
                  <TouchableOpacity
                    style={[styles.modernVoiceButton, isListening && styles.voiceButtonActive]}
                    onPress={() => {
                      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                      isListening ? stopVoiceRecognition() : startVoiceRecognition();
                    }}
                  >
                    <LottieIcon
                      name={isListening ? "voiceWave" : "voiceWave"}
                      size={18}
                      color={isListening ? "#FFFFFF" : Colors.brand}
                      enableHaptics={false}
                    />
                  </TouchableOpacity>
                </Animated.View>
              </View>
              {voiceText && (
                <Animated.Text entering={FadeInUp.duration(300)} style={styles.modernVoiceText}>
                  {voiceText}
                </Animated.Text>
              )}
            </View>
          </Animated.View>
        </View>
        {/* Beautiful Filter Chips - All Visible */}
        <Animated.View entering={FadeInLeft.delay(200).duration(600)} style={styles.modernFiltersSection}>
          <View style={styles.modernFiltersGrid}>
            {dietaryOptions.map((option, index) => (
              <Animated.View
                key={option.id}
                entering={ZoomIn.delay(index * 100).duration(500)}
                style={styles.filterChipWrapper}
              >
                <TouchableOpacity
                  style={[
                    styles.modernFilterChip,
                    selectedDietary.includes(option.id) && styles.modernFilterChipSelected
                  ]}
                  onPress={() => {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                    toggleDietaryFilter(option.id);
                  }}
                >
                  <View
                    style={[
                      styles.filterChipBlur,
                      selectedDietary.includes(option.id) && styles.filterChipSelected
                    ]}
                  >
                    <LottieIcon
                      name={option.id === 'vegetarian' ? 'broccoli' : option.id === 'vegan' ? 'apple' : option.id === 'gluten-free' ? 'avocado' : option.id === 'keto' ? 'fire' : option.id === 'low-carb' ? 'progressCircle' : 'heartbeat'}
                      size={16}
                      color={selectedDietary.includes(option.id) ? Colors.brandForeground : Colors.brand}
                      enableHaptics={false}
                    />
                    <Text style={[
                      styles.modernFilterText,
                      selectedDietary.includes(option.id) && styles.modernFilterTextSelected
                    ]}>
                      {option.label}
                    </Text>
                  </View>
                </TouchableOpacity>
              </Animated.View>
            ))}
          </View>
        </Animated.View>

        {/* Premium Favorite Recipes Collection */}
        {favoriteRecipes.length > 0 && (
          <Animated.View entering={FadeInUp.delay(250).duration(600)} style={styles.premiumFavoritesSection}>
            <View style={styles.premiumFavoritesHeader}>
              <View style={styles.premiumFavoritesHeaderText}>
                <View style={styles.premiumFavoritesTitleContainer}>
                  <Ionicons name="heart" size={24} color="#FF6B6B" />
                  <Text style={styles.premiumFavoritesTitle}>Curated Collection</Text>
                </View>
                <Text style={styles.premiumFavoritesSubtitle}>Your handpicked favorites</Text>
              </View>
              <TouchableOpacity
                style={styles.premiumViewAllButton}
                onPress={() => setShowAllFavorites(true)}
              >
                <Text style={styles.premiumViewAllText}>Explore All</Text>
                <Ionicons name="arrow-forward" size={18} color="#6B7C5A" />
              </TouchableOpacity>
            </View>

            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.premiumFavoritesScrollContent}
              decelerationRate="fast"
              snapToInterval={220}
              snapToAlignment="start"
            >
              {favoriteRecipes.map((favorite, index) => (
                <PremiumFavoriteCard
                  key={favorite.id}
                  favorite={favorite}
                  index={index}
                  favoriteImages={favoriteImages}
                  onPress={async () => {
                    try {
                      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                      console.log('Opening favorite recipe:', favorite.name);

                      // Get or generate recipe using RecipeCacheService
                      const cachedRecipe = await RecipeCacheService.getOrGenerateRecipe(favorite.name, 'generated');

                      // Convert to RecipeDetail format
                      const recipeData = {
                        id: cachedRecipe.id,
                        title: cachedRecipe.title,
                        description: cachedRecipe.description,
                        image: cachedRecipe.imageUrl,
                        imageUrl: cachedRecipe.imageUrl,
                        cookTime: cachedRecipe.cookTime,
                        servings: cachedRecipe.servings,
                        difficulty: cachedRecipe.difficulty,
                        calories: cachedRecipe.nutrition.calories,
                        ingredients: cachedRecipe.ingredients,
                        instructions: cachedRecipe.instructions,
                        nutrition: {
                          calories: cachedRecipe.nutrition.calories,
                          protein: `${cachedRecipe.nutrition.protein}g`,
                          carbs: `${cachedRecipe.nutrition.carbs}g`,
                          fat: `${cachedRecipe.nutrition.fat}g`,
                          fiber: '5g'
                        },
                        tags: cachedRecipe.tags
                      };

                      (navigation as any).navigate('RecipeDetail', { recipe: recipeData });
                    } catch (error) {
                      console.error('❌ Error loading favorite recipe:', error);
                      Alert.alert('Error', 'Could not load recipe details. Please try again.');
                    }
                  }}
                  onRemove={() => {
                    FavoritesService.removeFromFavorites(favorite.id).then(() => {
                      loadFavorites(); // Refresh the main list
                      console.log(`💔 Removed ${favorite.name} from favorites`);
                    });
                  }}
                />
              ))}
            </ScrollView>
          </Animated.View>
        )}

        {/* Modern Section Header */}
        <Animated.View entering={FadeInRight.delay(300).duration(600)} style={styles.modernSectionHeader}>
          <Text style={styles.modernSectionTitle}>
            {recipes.length > 0 ? `${recipes.length} Delicious Recipes` : 'Discover Amazing Recipes'}
          </Text>
          <Text style={styles.modernSectionSubtitle}>
            Handpicked recipes just for you
          </Text>
        </Animated.View>

        {/* Beautiful Loading State */}
        {loading && (
          <Animated.View entering={FadeInUp.duration(400)} style={styles.modernLoadingContainer}>
            <View style={styles.loadingCard}>
              <ActivityIndicator size="large" color="#6B7C5A" />
              <Text style={styles.loadingText}>Finding perfect recipes for you...</Text>
            </View>
          </Animated.View>
        )}

        {/* Beautiful Recipe Cards */}
        {recipes.length > 0 && (
          <Animated.View entering={FadeInUp.delay(400).duration(600)} style={styles.modernRecipesSection}>
            {recipes.map((recipe, index) => (
              <Animated.View
                key={recipe.id}
                entering={ZoomIn.delay(index * 150).duration(600)}
                style={styles.modernRecipeCard}
              >
                <TouchableOpacity
                  onPress={() => (navigation as any).navigate('RecipeDetail', { recipe })}
                  style={styles.modernRecipeCardButton}
                  activeOpacity={0.95}
                >
                  <ImageBackground
                    source={{
                      uri: recipe.imageUrl || recipe.image || 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80'
                    }}
                    style={styles.modernRecipeImage}
                    imageStyle={styles.modernRecipeImageStyle}
                  >
                    <LinearGradient
                      colors={['transparent', 'rgba(0,0,0,0.6)']}
                      style={styles.modernRecipeGradient}
                    >
                      <View style={styles.modernRecipeBadges}>
                        <View style={[styles.modernDifficultyBadge, { backgroundColor: getDifficultyColor(recipe.difficulty) }]}>
                          <Ionicons name={getDifficultyIcon(recipe.difficulty) as any} size={12} color="#FFFFFF" />
                          <Text style={styles.modernBadgeText}>{recipe.difficulty}</Text>
                        </View>
                        <View style={styles.modernTimeBadge}>
                          <Ionicons name="time" size={12} color="#FFFFFF" />
                          <Text style={styles.modernBadgeText}>{recipe.cookTime}</Text>
                        </View>
                      </View>
                    </LinearGradient>
                  </ImageBackground>

                  <View style={styles.modernRecipeContent}>
                    <Text style={styles.modernRecipeTitle} numberOfLines={2}>{recipe.title}</Text>
                    <Text style={styles.modernRecipeDescription} numberOfLines={2}>
                      {recipe.description}
                    </Text>

                    <View style={styles.modernRecipeStats}>
                      <View style={styles.modernStatItem}>
                        <Ionicons name="flame" size={16} color="#FF6B6B" />
                        <Text style={styles.modernStatText}>{recipe.calories} cal</Text>
                      </View>
                      {recipe.rating && (
                        <View style={styles.modernStatItem}>
                          <Ionicons name="star" size={16} color="#FFD700" />
                          <Text style={styles.modernStatText}>{recipe.rating}</Text>
                        </View>
                      )}
                      {recipe.servings && (
                        <View style={styles.modernStatItem}>
                          <Ionicons name="people" size={16} color="#4ECDC4" />
                          <Text style={styles.modernStatText}>{recipe.servings}</Text>
                        </View>
                      )}
                    </View>
                  </View>
                </TouchableOpacity>
              </Animated.View>
            ))}
          </Animated.View>
        )}

        {/* Beautiful Empty State */}
        {!loading && recipes.length === 0 && (
          <Animated.View entering={FadeInUp.delay(600).duration(600)} style={styles.modernEmptyState}>
            <View style={styles.modernEmptyStateCard}>
              <View style={styles.modernEmptyIcon}>
                <Ionicons name="restaurant" size={48} color="#6B7C5A" />
              </View>
              <Text style={styles.modernEmptyTitle}>No recipes found</Text>
              <Text style={styles.modernEmptyDescription}>
                Try adjusting your search to discover amazing recipes
              </Text>
              <TouchableOpacity
                style={styles.modernEmptyButton}
                onPress={searchRecipes}
              >
                <Text style={styles.modernEmptyButtonText}>Explore Recipes</Text>
                <Ionicons name="arrow-forward" size={16} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
          </Animated.View>
        )}



        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Filters Modal */}
      <ModernModal
        visible={showFilters}
        onClose={() => setShowFilters(false)}
        title="Filter Recipes"
        variant="bottom"
        size="lg"
      >
        <View style={styles.filtersModalContent}>
          <Text style={styles.modalSectionTitle}>Dietary Preferences</Text>
          <View style={styles.modalFiltersGrid}>
            {dietaryOptions.map((option) => (
              <FilterChip
                key={option.id}
                label={option.label}
                icon={option.icon as keyof typeof Ionicons.glyphMap}
                selected={selectedDietary.includes(option.id)}
                onPress={() => toggleDietaryFilter(option.id)}
                variant="dietary"
              />
            ))}
          </View>

          <Text style={styles.modalSectionTitle}>Difficulty Level</Text>
          <View style={styles.modalFiltersGrid}>
            {difficultyOptions.map((option) => (
              <FilterChip
                key={option.id}
                label={option.label}
                icon={option.icon as keyof typeof Ionicons.glyphMap}
                selected={selectedDifficulty === option.id}
                onPress={() => setSelectedDifficulty(selectedDifficulty === option.id ? '' : option.id)}
                variant="difficulty"
              />
            ))}
          </View>

          <Text style={styles.modalSectionTitle}>Cooking Time</Text>
          <View style={styles.modalFiltersGrid}>
            {timeOptions.map((option) => (
              <FilterChip
                key={option.id}
                label={option.label}
                selected={maxCookTime === option.id}
                onPress={() => setMaxCookTime(maxCookTime === option.id ? '' : option.id)}
                variant="time"
              />
            ))}
          </View>

          <View style={styles.modalButtons}>
            <ModernButton
              title="Clear All"
              onPress={clearFilters}
              variant="outline"
              size="md"
              style={styles.modalClearButton}
            />
            <ModernButton
              title="Apply Filters"
              onPress={() => {
                setShowFilters(false);
                searchRecipes();
              }}
              variant="primary"
              size="md"
              style={styles.modalApplyButton}
            />
          </View>
        </View>
      </ModernModal>

      {/* All Favorites Modal */}
      <ModernModal
        visible={showAllFavorites}
        onClose={() => setShowAllFavorites(false)}
        title="Your Favorite Recipes"
        variant="fullscreen"
        size="lg"
      >
        <View style={styles.favoritesModalContent}>
          {favoritesLoading ? (
            <View style={styles.favoritesModalLoading}>
              <ActivityIndicator size="large" color="#6B7C5A" />
              <Text style={styles.loadingText}>Loading your favorites...</Text>
            </View>
          ) : allFavorites.length === 0 ? (
            <View style={styles.favoritesModalEmpty}>
              <Ionicons name="heart-outline" size={64} color="#D1D5DB" />
              <Text style={styles.emptyFavoritesTitle}>No Favorites Yet</Text>
              <Text style={styles.emptyFavoritesSubtitle}>
                Start adding recipes to your favorites by tapping the heart icon
              </Text>
            </View>
          ) : (
            <FlatList
              data={allFavorites}
              keyExtractor={(item) => item.id}
              numColumns={2}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.favoritesModalGrid}
              columnWrapperStyle={styles.favoritesModalRow}
              onEndReached={loadMoreFavorites}
              onEndReachedThreshold={0.3}
              ListFooterComponent={() => (
                loadingMoreFavorites ? (
                  <View style={styles.loadMoreContainer}>
                    <ActivityIndicator size="small" color="#6B7C5A" />
                    <Text style={styles.loadMoreText}>Loading more...</Text>
                  </View>
                ) : null
              )}
              renderItem={({ item: favorite, index }) => (
                <Animated.View
                  entering={ZoomIn.delay(index * 50).duration(400)}
                  style={styles.favoritesModalCard}
                >
                  <TouchableOpacity
                    onPress={async () => {
                      try {
                        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                        console.log('Opening favorite recipe:', favorite.name);

                        // Get or generate recipe using RecipeCacheService
                        const cachedRecipe = await RecipeCacheService.getOrGenerateRecipe(favorite.name, 'generated');

                        // Convert to RecipeDetail format
                        const recipeData = {
                          id: cachedRecipe.id,
                          title: cachedRecipe.title,
                          description: cachedRecipe.description,
                          image: cachedRecipe.imageUrl,
                          imageUrl: cachedRecipe.imageUrl,
                          cookTime: cachedRecipe.cookTime,
                          servings: cachedRecipe.servings,
                          difficulty: cachedRecipe.difficulty,
                          calories: cachedRecipe.nutrition.calories,
                          ingredients: cachedRecipe.ingredients,
                          instructions: cachedRecipe.instructions,
                          nutrition: {
                            calories: cachedRecipe.nutrition.calories,
                            protein: `${cachedRecipe.nutrition.protein}g`,
                            carbs: `${cachedRecipe.nutrition.carbs}g`,
                            fat: `${cachedRecipe.nutrition.fat}g`,
                            fiber: '5g'
                          },
                          tags: cachedRecipe.tags
                        };

                        // Close modal and navigate
                        setShowAllFavorites(false);
                        (navigation as any).navigate('RecipeDetail', { recipe: recipeData });
                      } catch (error) {
                        console.error('❌ Error loading favorite recipe:', error);
                        Alert.alert('Error', 'Could not load recipe details. Please try again.');
                      }
                    }}
                    style={styles.favoritesModalCardButton}
                    activeOpacity={0.9}
                  >
                    <View style={styles.favoritesModalCardContainer}>
                      <ImageBackground
                        source={{
                          uri: favoriteImages[favorite.name] || favorite.imageUrl || 'https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=400&h=300&fit=crop&crop=center&q=80'
                        }}
                        style={styles.favoritesModalImage}
                        imageStyle={styles.favoritesModalImageStyle}
                      >
                        <View style={styles.favoritesModalOverlay}>
                          <TouchableOpacity
                            style={styles.favoritesModalHeartButton}
                            onPress={(e) => {
                              e.stopPropagation();
                              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                              FavoritesService.removeFromFavorites(favorite.id).then(() => {
                                loadAllFavorites(1, true); // Refresh the modal list
                                loadFavorites(); // Refresh the main screen list
                                console.log(`💔 Removed ${favorite.name} from favorites`);
                              });
                            }}
                          >
                            <Ionicons name="heart" size={18} color="#FF6B6B" />
                          </TouchableOpacity>
                        </View>
                      </ImageBackground>

                      <View style={styles.favoritesModalCardContent}>
                        <Text style={styles.favoritesModalName} numberOfLines={2}>
                          {favorite.name}
                        </Text>
                        <View style={styles.favoritesModalMeta}>
                          {favorite.cookingTime && (
                            <View style={styles.favoritesModalMetaItem}>
                              <Ionicons name="time" size={12} color="#6B7C5A" />
                              <Text style={styles.favoritesModalMetaText}>
                                {favorite.cookingTime}
                              </Text>
                            </View>
                          )}
                          {favorite.calories && (
                            <View style={styles.favoritesModalMetaItem}>
                              <Ionicons name="flame" size={12} color="#8B9A7A" />
                              <Text style={styles.favoritesModalMetaText}>
                                {favorite.calories} cal
                              </Text>
                            </View>
                          )}
                        </View>
                      </View>
                    </View>
                  </TouchableOpacity>
                </Animated.View>
              )}
            />
          )}
        </View>
      </ModernModal>
    </View>
  );
};



const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },

  // Beautiful Background with Subtle Image
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  whiteOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.87)', // Reduced opacity by 10%
  },

  // Modern Search Header
  modernSearchHeader: {
    paddingTop: 60, // Reduced by 20px
    paddingHorizontal: 20,
    paddingBottom: 24,
    backgroundColor: 'transparent',
  },
  searchSection: {
    alignItems: 'center',
    marginBottom: 8,
  },
  headerTitle: {
    fontSize: 32,
    fontWeight: '700',
    color: '#1a202c',
    marginBottom: 4,
    letterSpacing: -0.8,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#6B7280',
    fontWeight: '500',
    marginBottom: 24,
    textAlign: 'center',
  },
  modernSearchContainer: {
    width: '100%',
  },
  searchInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.card,
    borderRadius: 16,
    paddingHorizontal: 20,
    paddingVertical: 16,
    shadowColor: Colors.cardShadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  searchIcon: {
    marginRight: 12,
  },
  modernSearchInput: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    color: '#1a202c',
  },
  searchButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#6B7C5A',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12,
  },
  modernVoiceButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  voiceButtonActive: {
    backgroundColor: '#6B7C5A',
  },
  modernVoiceText: {
    fontSize: 14,
    color: '#6B7C5A',
    fontStyle: 'italic',
    marginTop: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100,
  },

  // Modern Filter Chips
  modernFiltersSection: {
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  modernFiltersGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  filterChipWrapper: {
    marginBottom: 8,
  },
  modernFilterChip: {
    borderRadius: 32,
    marginRight: 12,
    borderWidth: 1,
    borderColor: Colors.border,
    shadowColor: Colors.shadowMd,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
    overflow: 'hidden',
  },
  filterChipBlur: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: Colors.backgroundTertiary,
  },
  filterChipSelected: {
    backgroundColor: Colors.brandMuted,
    borderColor: Colors.brand,
  },
  modernFilterChipSelected: {
    backgroundColor: '#6B7C5A',
    borderColor: '#6B7C5A',
    shadowColor: '#6B7C5A',
    shadowOpacity: 0.3,
  },
  filterIcon: {
    marginRight: 4,
  },
  modernFilterText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2d3748',
  },
  modernFilterTextSelected: {
    color: '#FFFFFF',
  },

  // Modern Section Header
  modernSectionHeader: {
    paddingHorizontal: 20,
    marginBottom: 24,
    alignItems: 'center',
  },
  modernSectionTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1a202c',
    marginBottom: 4,
    letterSpacing: -0.5,
    textAlign: 'center',
  },
  modernSectionSubtitle: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
    textAlign: 'center',
  },

  // Modern Loading State
  modernLoadingContainer: {
    paddingVertical: 60,
    alignItems: 'center',
  },
  loadingCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 24,
    padding: 32,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
    fontWeight: '500',
    marginTop: 16,
    textAlign: 'center',
  },

  // Beautiful Modern Recipe Cards
  modernRecipesSection: {
    paddingHorizontal: 20,
  },
  modernRecipeCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 32,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 15,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    overflow: 'hidden',
  },
  modernRecipeCardButton: {
    flex: 1,
  },
  modernRecipeImage: {
    height: 200,
    width: '100%',
  },
  modernRecipeImageStyle: {
    borderTopLeftRadius: 32,
    borderTopRightRadius: 32,
  },
  modernRecipeGradient: {
    flex: 1,
    justifyContent: 'space-between',
    padding: 20,
  },
  modernRecipeBadges: {
    flexDirection: 'row',
    gap: 12,
    alignItems: 'flex-start',
  },
  modernDifficultyBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    gap: 4,
  },
  modernTimeBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    gap: 4,
  },
  modernBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  modernRecipeContent: {
    padding: 24,
  },
  modernRecipeTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1a202c',
    marginBottom: 8,
    letterSpacing: -0.3,
    lineHeight: 26,
  },
  modernRecipeDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: 16,
    fontWeight: '500',
  },
  modernRecipeStats: {
    flexDirection: 'row',
    gap: 16,
    alignItems: 'center',
  },
  modernStatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  modernStatText: {
    fontSize: 14,
    color: '#4a5568',
    fontWeight: '600',
  },

  // Beautiful Empty State
  modernEmptyState: {
    paddingVertical: 60,
    alignItems: 'center',
  },
  modernEmptyStateCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 32,
    padding: 40,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 15,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    maxWidth: 320,
  },
  modernEmptyIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  modernEmptyTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1a202c',
    marginBottom: 8,
    letterSpacing: -0.3,
    textAlign: 'center',
  },
  modernEmptyDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: 24,
    fontWeight: '500',
    textAlign: 'center',
  },
  modernEmptyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    backgroundColor: '#6B7C5A',
    paddingHorizontal: 24,
    paddingVertical: 14,
    borderRadius: 24,
    shadowColor: '#6B7C5A',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
  },
  modernEmptyButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },

  // Billion-Dollar Search Header
  searchHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingTop: 80,
    paddingBottom: 24,
    backgroundColor: '#FAFAFA',
    gap: 16,
  },
  searchContainer: {
    flex: 1,
  },
  searchInput: {
    marginBottom: 0,
  },
  voiceText: {
    fontSize: FontSizes.sm,
    color: Colors.brand,
    fontStyle: 'italic',
    marginTop: Spacing.xs,
    textAlign: 'center',
  },
  filterButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },

  // Enhanced Search Input
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 24,
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },

  // Voice Button (for SearchHeader component)
  voiceButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },

  // Enhanced Filter Chips
  filtersSection: {
    marginBottom: 24,
  },
  filtersContainer: {
    paddingHorizontal: 20,
    gap: 12,
  },
  filterChip: {
    borderRadius: 20,
    marginRight: 12,
  },
  filterChipButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingHorizontal: 16,
    paddingVertical: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.2)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  filterChipButtonSelected: {
    backgroundColor: '#6B7C5A',
    borderColor: '#6B7C5A',
  },
  filterChipIcon: {
    marginRight: 4,
  },
  filterChipText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2d3748',
  },
  filterChipTextSelected: {
    color: '#FFFFFF',
  },

  // View Mode Section
  viewModeSection: {
    paddingHorizontal: Spacing.xl,
    marginBottom: Spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
  },
  viewModeToggle: {
    flexDirection: 'row',
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.lg,
    padding: Spacing.xs,
  },
  viewModeButton: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
  },
  viewModeButtonActive: {
    backgroundColor: Colors.brand,
  },

  // Loading
  loadingContainer: {
    paddingVertical: Spacing.xxxl,
    alignItems: 'center',
  },

  // Enhanced Featured Section
  featuredSection: {
    marginBottom: 32,
  },
  featuredContainer: {
    paddingHorizontal: 20,
    gap: 16,
  },
  featuredCard: {
    width: width * 0.85,
    height: 280,
    borderRadius: 32,
    overflow: 'hidden',
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 15,
  },
  featuredCardButton: {
    flex: 1,
  },
  featuredCardImage: {
    flex: 1,
  },
  featuredCardImageStyle: {
    borderRadius: 32,
  },
  featuredCardGradient: {
    flex: 1,
    justifyContent: 'space-between',
    padding: 24,
  },
  featuredCardContent: {
    flex: 1,
    justifyContent: 'space-between',
  },
  featuredCardBadges: {
    flexDirection: 'row',
    gap: 12,
    alignItems: 'center',
  },
  featuredCardInfo: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  featuredCardTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    marginBottom: 8,
    letterSpacing: -0.5,
  },
  featuredCardDescription: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.9,
    marginBottom: 16,
    fontWeight: '500',
  },
  featuredCardStats: {
    flexDirection: 'row',
    gap: 16,
    alignItems: 'center',
  },



  // Recipe Card Styles
  recipeCardButton: {
    flex: 1,
  },
  recipeCardImage: {
    height: 160,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    overflow: 'hidden',
  },
  cardImageBackground: {
    flex: 1,
  },
  cardImageStyle: {
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },
  cardImageOverlay: {
    flex: 1,
    justifyContent: 'space-between',
    padding: 16,
  },
  cardBadges: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'flex-start',
  },
  recipeCardContent: {
    padding: 20,
  },
  recipeCardTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1a202c',
    marginBottom: 6,
    letterSpacing: -0.3,
  },
  recipeCardDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 18,
    marginBottom: 12,
  },
  recipeCardStats: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  ingredientPreview: {
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderRadius: 12,
    padding: 12,
  },
  ingredientPreviewText: {
    fontSize: 12,
    color: '#6B7C5A',
    fontWeight: '500',
  },

  // Badge Styles
  difficultyBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  difficultyBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  timeBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    gap: 4,
  },
  timeBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  ratingBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 215, 0, 0.2)',
    gap: 4,
  },
  ratingBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFD700',
  },

  // Modal Styles
  filtersModalContent: {
    padding: 24,
  },
  modalSectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1a202c',
    marginBottom: 16,
    marginTop: 16,
  },
  modalFiltersGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 8,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 16,
    marginTop: 32,
  },
  modalClearButton: {
    flex: 1,
  },
  modalApplyButton: {
    flex: 1,
  },

  // Additional Recipe Card Styles
  recipeHeader: {
    padding: 20,
    paddingBottom: 12,
  },
  recipeInfo: {
    marginBottom: 12,
  },
  recipeTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1a202c',
    marginBottom: 6,
    letterSpacing: -0.3,
  },
  recipeDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 18,
  },
  recipeTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  tag: {
    backgroundColor: Colors.brand,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    shadowColor: Colors.brand,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  tagText: {
    fontSize: 13,
    color: Colors.white,
    fontWeight: '700',
    letterSpacing: 0.3,
  },
  difficultyTag: {
    backgroundColor: Colors.warning,
    shadowColor: Colors.warning,
  },
  difficultyText: {
    color: Colors.white,
    fontSize: 13,
    fontWeight: '700',
    letterSpacing: 0.3,
  },
  recipeFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  ingredientsLabel: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
  },
  viewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  viewButtonText: {
    fontSize: 14,
    color: '#6B7C5A',
    fontWeight: '600',
  },

  // Stat Item Styles
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statText: {
    fontSize: 14,
    color: '#4a5568',
    fontWeight: '600',
  },

  // Recipe Card Main Style
  recipeCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 24,
    marginHorizontal: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    overflow: 'hidden',
  },

  // Dietary Chip Styles
  dietaryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderWidth: 1,
    borderColor: '#6B7C5A',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 12,
    gap: 8,
  },
  dietaryChipSelected: {
    backgroundColor: '#6B7C5A',
  },
  dietaryChipText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7C5A',
  },
  dietaryChipTextSelected: {
    color: '#FFFFFF',
  },

  // Recipe Stats
  recipeStats: {
    flexDirection: 'row',
    gap: 16,
  },

  // Bottom Spacing
  bottomSpacing: {
    height: 100,
  },

  // Premium Favorites Section Styles
  premiumFavoritesSection: {
    marginHorizontal: 20,
    marginBottom: 32,
  },
  premiumFavoritesHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
    gap: 16, // Add gap to prevent overlap
  },
  premiumFavoritesHeaderText: {
    flex: 1,
    marginRight: 8, // Add margin to prevent overlap
  },
  premiumFavoritesTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4,
  },
  premiumFavoritesTitle: {
    fontSize: 24,
    fontWeight: '800',
    color: '#6B7C5A',
    letterSpacing: -0.6,
  },
  premiumFavoritesSubtitle: {
    fontSize: 15,
    fontWeight: '500',
    color: '#8B9A7A',
    lineHeight: 20,
  },
  premiumViewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    paddingHorizontal: 12, // Reduced padding to fit better
    paddingVertical: 8, // Reduced padding
    borderRadius: 16, // Smaller radius
    gap: 4, // Reduced gap
    minWidth: 100, // Ensure minimum width
    justifyContent: 'center',
  },
  premiumViewAllText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7C5A',
  },
  premiumFavoritesScrollContent: {
    paddingRight: 20,
    gap: 16,
  },

  // Premium Favorite Card Styles
  premiumFavoriteCard: {
    width: 200,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 24,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.15)',
    position: 'relative',
  },
  premiumFavoriteGlow: {
    position: 'absolute',
    top: -4,
    left: -4,
    right: -4,
    bottom: -4,
    backgroundColor: 'rgba(107, 124, 90, 0.2)',
    borderRadius: 28,
    zIndex: -1,
  },
  premiumFavoriteCardButton: {
    flex: 1,
  },
  premiumFavoriteImageContainer: {
    position: 'relative',
  },
  premiumFavoriteImage: {
    height: 140,
    justifyContent: 'flex-end',
  },
  premiumFavoriteImageStyle: {
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },
  premiumFavoriteImageGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 60,
  },
  premiumFavoriteHeartButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  premiumFavoriteBadge: {
    position: 'absolute',
    top: 12,
    left: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    flexDirection: 'row',
    alignItems: 'center',
  },
  premiumFavoriteContent: {
    padding: 16,
  },
  premiumFavoriteName: {
    fontSize: 16,
    fontWeight: '700',
    color: '#6B7C5A',
    lineHeight: 22,
    marginBottom: 8,
    letterSpacing: -0.3,
  },
  premiumFavoriteMeta: {
    flexDirection: 'row',
    gap: 12,
  },
  premiumFavoriteMetaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  premiumFavoriteMetaText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#8B9A7A',
  },

  // Legacy Favorites Section Styles (keeping for compatibility)
  favoritesSection: {
    marginHorizontal: 20,
    marginBottom: 24,
  },
  favoritesSectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  favoritesSectionTitle: {
    fontSize: 22,
    fontWeight: '700',
    color: '#6B7C5A',
    letterSpacing: -0.5,
  },
  favoritesSectionSubtitle: {
    fontSize: 14,
    color: '#8B9A7A',
    marginTop: 2,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#6B7C5A',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '600',
    color: 'white',
    marginRight: 4,
  },
  favoritesScrollContent: {
    paddingRight: 20,
  },
  favoriteCard: {
    marginRight: 16,
    width: 160,
    backgroundColor: 'white',
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#8B9A7A',
  },
  favoriteCardButton: {
    flex: 1,
  },
  favoriteImage: {
    height: 80,
    justifyContent: 'flex-end',
  },
  favoriteImageStyle: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  favoriteOverlay: {
    position: 'absolute',
    top: 0,
    right: 0,
    left: 0,
    bottom: 0,
    justifyContent: 'flex-start',
    alignItems: 'flex-end',
    padding: 8,
  },
  favoriteContent: {
    padding: 12,
    backgroundColor: 'white',
  },
  favoriteName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7C5A',
    lineHeight: 18,
    marginBottom: 4,
  },
  favoriteTime: {
    fontSize: 12,
    color: '#8B9A7A',
  },
  favoriteHeartButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#8B9A7A',
  },

  // Favorites Modal Styles
  favoritesModalContent: {
    flex: 1,
    padding: 20,
  },
  favoritesModalLoading: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  favoritesModalEmpty: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyFavoritesTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#6B7C5A',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyFavoritesSubtitle: {
    fontSize: 16,
    color: '#8B9A7A',
    textAlign: 'center',
    lineHeight: 24,
  },
  favoritesModalGrid: {
    paddingBottom: 20,
  },
  favoritesModalRow: {
    justifyContent: 'space-between',
    paddingHorizontal: 4,
  },
  favoritesModalCard: {
    width: '48%',
    marginBottom: 16,
    backgroundColor: 'white',
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#8B9A7A',
  },
  favoritesModalCardButton: {
    flex: 1,
  },
  favoritesModalCardContainer: {
    flex: 1,
  },
  favoritesModalImage: {
    height: 120,
    justifyContent: 'flex-end',
  },
  favoritesModalImageStyle: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  favoritesModalOverlay: {
    position: 'absolute',
    top: 0,
    right: 0,
    left: 0,
    bottom: 0,
    justifyContent: 'flex-start',
    alignItems: 'flex-end',
    padding: 12,
  },
  favoritesModalHeartButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#8B9A7A',
  },
  favoritesModalCardContent: {
    padding: 12,
    backgroundColor: 'white',
  },
  favoritesModalName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B7C5A',
    lineHeight: 20,
    marginBottom: 8,
  },
  favoritesModalMeta: {
    flexDirection: 'row',
    gap: 12,
  },
  favoritesModalMetaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  favoritesModalMetaText: {
    fontSize: 12,
    color: '#8B9A7A',
  },
  loadMoreContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
    gap: 8,
  },
  loadMoreText: {
    fontSize: 14,
    color: '#8B9A7A',
  },

  // Additional overlay styles for removing gradients
  featuredCardOverlay: {
    flex: 1,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    justifyContent: 'space-between',
    padding: 20,
  },

});

export default RecipesScreenModern;
