import React, { useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedProps,
  withTiming,
  withSpring,
  interpolate,
  Easing,
} from 'react-native-reanimated';
import Svg, { Circle, Defs, LinearGradient, Stop } from 'react-native-svg';
import { Colors } from '../constants/Colors';

const AnimatedCircle = Animated.createAnimatedComponent(Circle);

interface WeightProgressCircleProps {
  progress: number; // 0-100
  size?: number;
  strokeWidth?: number;
  currentWeight: number;
  targetWeight: number;
  weightLost: number;
  isOnTrack: boolean;
  style?: any;
}

const WeightProgressCircle: React.FC<WeightProgressCircleProps> = ({
  progress,
  size = 200,
  strokeWidth = 12,
  currentWeight,
  targetWeight,
  weightLost,
  isOnTrack,
  style,
}) => {
  const animatedProgress = useSharedValue(0);
  const scaleValue = useSharedValue(0.8);

  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;

  useEffect(() => {
    // Animate progress with spring
    animatedProgress.value = withTiming(progress, {
      duration: 1500,
      easing: Easing.out(Easing.cubic),
    });

    // Scale animation for entrance
    scaleValue.value = withSpring(1, {
      damping: 15,
      stiffness: 150,
    });
  }, [progress]);

  const animatedProps = useAnimatedProps(() => {
    const strokeDashoffset = interpolate(
      animatedProgress.value,
      [0, 100],
      [circumference, 0]
    );

    return {
      strokeDashoffset,
      transform: [{ scale: scaleValue.value }],
    };
  });

  const getProgressColor = () => {
    if (progress >= 75) return Colors.successVibrant; // Green for excellent progress
    if (progress >= 50) return Colors.brand; // Brand green for good progress
    if (progress >= 25) return Colors.warningVibrant; // Orange for moderate progress
    return Colors.errorVibrant; // Red for low progress
  };

  const getMotivationalMessage = () => {
    if (progress >= 90) return 'Almost there! 🎉';
    if (progress >= 75) return 'Excellent progress! 💪';
    if (progress >= 50) return 'Great job! Keep going! 🔥';
    if (progress >= 25) return 'Good start! Stay focused! 💯';
    return 'Your journey begins! 🌟';
  };

  const getStatusIcon = () => {
    if (progress >= 75) return '🏆';
    if (progress >= 50) return '⭐';
    if (progress >= 25) return '📈';
    return '🎯';
  };

  return (
    <View style={[styles.container, style]}>
      <Animated.View style={[styles.circleContainer, { transform: [{ scale: scaleValue }] }]}>
        <Svg width={size} height={size} style={styles.svg}>
          <Defs>
            <LinearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor={getProgressColor()} stopOpacity="1" />
              <Stop offset="100%" stopColor={getProgressColor()} stopOpacity="0.7" />
            </LinearGradient>
          </Defs>
          
          {/* Background Circle */}
          <Circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={Colors.brandMuted}
            strokeWidth={strokeWidth}
            fill="none"
          />
          
          {/* Progress Circle */}
          <AnimatedCircle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="url(#progressGradient)"
            strokeWidth={strokeWidth}
            fill="none"
            strokeLinecap="round"
            strokeDasharray={circumference}
            animatedProps={animatedProps}
            transform={`rotate(-90 ${size / 2} ${size / 2})`}
          />
        </Svg>

        {/* Center Content */}
        <View style={styles.centerContent}>
          <Text style={styles.statusIcon}>{getStatusIcon()}</Text>
          <Text style={styles.progressText}>{Math.round(progress)}%</Text>
          <Text style={styles.progressLabel}>Complete</Text>
          
          <View style={styles.weightStats}>
            <View style={styles.weightStat}>
              <Text style={styles.weightValue}>{currentWeight.toFixed(1)}</Text>
              <Text style={styles.weightLabel}>Current</Text>
            </View>
            <View style={styles.weightDivider} />
            <View style={styles.weightStat}>
              <Text style={styles.weightValue}>{targetWeight.toFixed(1)}</Text>
              <Text style={styles.weightLabel}>Target</Text>
            </View>
          </View>

          <View style={styles.lostContainer}>
            <Text style={[styles.lostValue, { color: getProgressColor() }]}>-{weightLost.toFixed(1)} kg</Text>
            <Text style={styles.lostLabel}>Lost</Text>
          </View>
        </View>
      </Animated.View>

      {/* Motivational Message */}
      <View style={styles.messageContainer}>
        <Text style={[styles.motivationalMessage, { color: getProgressColor() }]}>
          {getMotivationalMessage()}
        </Text>
        <View style={[styles.statusIndicator, { backgroundColor: isOnTrack ? Colors.successVibrant : Colors.warningVibrant }]}>
          <Text style={styles.statusText}>
            {isOnTrack ? 'On Track' : 'Needs Focus'}
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  circleContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  svg: {
    position: 'absolute',
  },
  centerContent: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
  },
  statusIcon: {
    fontSize: 24,
    marginBottom: 4,
  },
  progressText: {
    fontSize: 32,
    fontWeight: '800',
    color: Colors.brand,
    marginBottom: 2,
  },
  progressLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.mutedForeground,
    marginBottom: 12,
  },
  weightStats: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  weightStat: {
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  weightValue: {
    fontSize: 16,
    fontWeight: '700',
    color: Colors.foreground,
  },
  weightLabel: {
    fontSize: 11,
    fontWeight: '500',
    color: Colors.mutedForeground,
    marginTop: 2,
  },
  weightDivider: {
    width: 1,
    height: 20,
    backgroundColor: Colors.border,
    marginHorizontal: 8,
  },
  lostContainer: {
    alignItems: 'center',
    marginTop: 4,
  },
  lostValue: {
    fontSize: 18,
    fontWeight: '700',
    // Color set dynamically based on progress
  },
  lostLabel: {
    fontSize: 11,
    fontWeight: '500',
    color: Colors.mutedForeground,
    marginTop: 1,
  },
  messageContainer: {
    alignItems: 'center',
    marginTop: 20,
    gap: 8,
  },
  motivationalMessage: {
    fontSize: 16,
    fontWeight: '700',
    textAlign: 'center',
  },
  statusIndicator: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.white,
  },
});

export default WeightProgressCircle;
