import React, { useEffect } from 'react';
import { View, StyleSheet, Dimensions, Image, ImageBackground } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSequence,
  withRepeat,
  runOnJS,
  withSpring,
} from 'react-native-reanimated';

const { width, height } = Dimensions.get('window');

interface SplashScreenProps {
  onAnimationComplete?: () => void;
}

const SplashScreen: React.FC<SplashScreenProps> = ({ onAnimationComplete }) => {
  const logoScale = useSharedValue(0);
  const logoOpacity = useSharedValue(0);
  const logoTranslateX = useSharedValue(-width); // Start from left side
  const backgroundOpacity = useSharedValue(1);

  useEffect(() => {
    // Start the animation sequence with smooth slide-in from left
    logoOpacity.value = withTiming(1, { duration: 600 });
    logoTranslateX.value = withSpring(0, {
      damping: 15,
      stiffness: 100
    });
    logoScale.value = withSequence(
      withTiming(1.1, { duration: 800 }),
      withTiming(1, { duration: 400 })
    );

    // After 2.5 seconds, start fade out and call completion
    setTimeout(() => {
      backgroundOpacity.value = withTiming(0, { duration: 500 }, () => {
        if (onAnimationComplete) {
          runOnJS(onAnimationComplete)();
        }
      });
    }, 2500);
  }, []);

  const logoAnimatedStyle = useAnimatedStyle(() => ({
    opacity: logoOpacity.value,
    transform: [
      { scale: logoScale.value },
      { translateX: logoTranslateX.value }
    ],
  }));

  const backgroundAnimatedStyle = useAnimatedStyle(() => ({
    opacity: backgroundOpacity.value,
  }));

  return (
    <Animated.View style={[styles.container, backgroundAnimatedStyle]}>
      {/* Beautiful Background with New Screens Background Image */}
      <ImageBackground
        source={require('../../assets/screens background.jpg')}
        style={styles.backgroundContainer}
        resizeMode="cover"
      >
        <View style={styles.whiteOverlay} />
      </ImageBackground>

      {/* Brand gradient overlay for splash effect */}
      <LinearGradient
        colors={['rgba(107, 124, 90, 0.8)', 'rgba(139, 154, 122, 0.6)', 'rgba(107, 124, 90, 0.8)']}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      {/* Logo */}
      <Animated.View style={[styles.logoContainer, logoAnimatedStyle]}>
        <Image
          source={require('../../assets/image final.png')}
          style={styles.logoImage}
          resizeMode="contain"
        />
      </Animated.View>

      {/* Loading indicator */}
      <View style={styles.loadingContainer}>
        <View style={styles.loadingBar}>
          <Animated.View style={[styles.loadingProgress]} />
        </View>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },

  // Beautiful Background
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  whiteOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.2)', // Reduced opacity by 10%
  },

  gradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },

  logoContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 80,
  },
  logoImage: {
    width: 200,
    height: 80,
    tintColor: '#ffffff', // Make it white for splash screen
  },
  loadingContainer: {
    position: 'absolute',
    bottom: 100,
    left: 50,
    right: 50,
    alignItems: 'center',
  },
  loadingBar: {
    width: '100%',
    height: 3,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 1.5,
    overflow: 'hidden',
  },
  loadingProgress: {
    height: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 1.5,
    width: '100%',
  },
});

export default SplashScreen;
