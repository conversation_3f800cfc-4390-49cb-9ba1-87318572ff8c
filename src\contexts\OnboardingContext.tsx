import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useProfile } from './ProfileContext';
import WeightGoalTracker from '../services/WeightGoalTracker';

export interface OnboardingData {
  // Personal Information
  name: string;
  age: number;
  gender: 'male' | 'female' | 'other' | '';
  height: number; // in cm
  weight: number; // in kg

  // Health Goals
  weightGoal: 'lose' | 'maintain' | 'gain' | '';
  targetWeight: number;
  timeframe: number; // weeks to achieve weight goal
  fitnessObjectives: string[];
  healthConditions: string[];
  
  // Dietary Preferences
  dietaryPreferences: string[];
  
  // Allergens & Restrictions
  allergens: string[];
  dietaryRestrictions: string[];
  
  // Cuisine Preferences
  cuisinePreferences: string[];
  
  // Activity Level
  activityLevel: 'sedentary' | 'lightly_active' | 'moderately_active' | 'very_active' | '';
  
  // Nutrition Goals
  caloriesGoal: number;
  proteinGoal: number;
  carbsPercentage: number;
  fatPercentage: number;
  
  // Notification Settings
  mealReminders: boolean;
  waterReminders: boolean;
  progressUpdates: boolean;

  // Meal Timing Preferences
  breakfastTime: string;
  lunchTime: string;
  dinnerTime: string;
  
  // Profile Picture
  profileImage: string | null;
  
  // Progress
  currentStep: number;
  isComplete: boolean;
}

interface OnboardingContextType {
  data: OnboardingData;
  updateData: (field: keyof OnboardingData, value: any) => void;
  updateMultipleFields: (updates: Partial<OnboardingData>) => void;
  nextStep: () => void;
  previousStep: () => void;
  goToStep: (step: number) => void;
  saveProgress: () => Promise<void>;
  loadProgress: () => Promise<void>;
  completeOnboarding: () => Promise<void>;
  getProgressPercentage: () => number;
  canGoNext: () => boolean;
  canGoPrevious: () => boolean;
}

const defaultData: OnboardingData = {
  name: '',
  age: 0,
  gender: '',
  height: 0,
  weight: 0,
  weightGoal: '',
  targetWeight: 0,
  timeframe: 12, // Default 12 weeks
  fitnessObjectives: [],
  healthConditions: [],
  dietaryPreferences: [],
  allergens: [],
  dietaryRestrictions: [],
  cuisinePreferences: [],
  activityLevel: '',
  caloriesGoal: 2000,
  proteinGoal: 150,
  carbsPercentage: 45,
  fatPercentage: 30,
  mealReminders: true,
  waterReminders: true,
  progressUpdates: true,
  breakfastTime: '08:00',
  lunchTime: '13:00',
  dinnerTime: '19:00',
  profileImage: null,
  currentStep: 0,
  isComplete: false,
};

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

export const OnboardingProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [data, setData] = useState<OnboardingData>(defaultData);
  const { updateProfileBatch } = useProfile();
  
  const totalSteps = 11; // Total number of onboarding screens

  useEffect(() => {
    loadProgress();
  }, []);

  const updateData = (field: keyof OnboardingData, value: any) => {
    setData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateMultipleFields = (updates: Partial<OnboardingData>) => {
    setData(prev => ({
      ...prev,
      ...updates
    }));
  };

  const nextStep = () => {
    if (data.currentStep < totalSteps - 1) {
      updateData('currentStep', data.currentStep + 1);
    }
  };

  const previousStep = () => {
    if (data.currentStep > 0) {
      updateData('currentStep', data.currentStep - 1);
    }
  };

  const goToStep = (step: number) => {
    if (step >= 0 && step < totalSteps) {
      updateData('currentStep', step);
    }
  };

  const saveProgress = async () => {
    try {
      await AsyncStorage.setItem('onboardingProgress', JSON.stringify(data));
    } catch (error) {
      console.error('Error saving onboarding progress:', error);
    }
  };

  const loadProgress = async () => {
    try {
      const savedProgress = await AsyncStorage.getItem('onboardingProgress');
      if (savedProgress) {
        const parsedData = JSON.parse(savedProgress);
        setData(parsedData);
      }
    } catch (error) {
      console.error('Error loading onboarding progress:', error);
    }
  };

  const completeOnboarding = async () => {
    try {
      // Validate onboarding data before proceeding
      if (!data.name || !data.age || !data.height || !data.weight) {
        throw new Error('Missing required onboarding data');
      }

      // Create profile updates with validation
      const profileUpdates = {
        name: data.name.trim(),
        age: Math.max(1, Math.min(150, data.age)),
        gender: data.gender,
        height: Math.max(50, Math.min(300, data.height)),
        weight: Math.max(20, Math.min(500, data.weight)),
        caloriesGoal: Math.max(800, Math.min(5000, data.caloriesGoal)),
        proteinGoal: Math.max(20, Math.min(400, data.proteinGoal)),
        waterGoal: 8, // Default water goal
        stepsGoal: 10000, // Default steps goal
        dietaryPreferences: Array.isArray(data.dietaryPreferences) ? data.dietaryPreferences : [],
        allergies: Array.isArray(data.allergens) ? data.allergens : [],
        preferredCuisines: Array.isArray(data.cuisinePreferences) ? data.cuisinePreferences : [],
        activityLevel: data.activityLevel || 'moderately_active',
        weightGoal: data.weightGoal || 'maintain',
        targetWeight: data.targetWeight || data.weight,
        fitnessObjectives: Array.isArray(data.fitnessObjectives) ? data.fitnessObjectives : [],
        healthConditions: Array.isArray(data.healthConditions) ? data.healthConditions : [],
        notificationSettings: {
          mealReminders: Boolean(data.mealReminders),
          waterReminders: Boolean(data.waterReminders),
          workoutReminders: false, // Default value
          progressUpdates: Boolean(data.progressUpdates),
        },
        preferredMealTimes: {
          breakfast: data.breakfastTime || '08:00',
          lunch: data.lunchTime || '13:00',
          dinner: data.dinnerTime || '19:00',
          snack: '15:00', // Default snack time
        },
        isProfileComplete: true,
      };

      console.log('🔄 Completing onboarding with validated data:', profileUpdates);

      // CRITICAL: Update profile context with batch update and wait for completion
      console.log('📝 Updating profile batch...');
      await updateProfileBatch(profileUpdates);
      console.log('✅ Profile batch update completed');

      // Save profile image if provided
      if (data.profileImage) {
        console.log('🖼️ Saving profile image...');
        await AsyncStorage.setItem('profileImage', data.profileImage);
        console.log('✅ Profile image saved');
      }

      // CRITICAL: Ensure all AsyncStorage operations complete before marking as complete
      console.log('💾 Saving onboarding completion status...');
      await AsyncStorage.setItem('onboardingComplete', 'true');
      console.log('✅ Onboarding completion status saved');

      // Clean up onboarding progress
      await AsyncStorage.removeItem('onboardingProgress');
      console.log('🧹 Onboarding progress cleaned up');

      // Initialize WeightGoalTracker if user has weight loss/gain goals
      if (data.weightGoal && data.weightGoal !== 'maintain' && data.targetWeight && data.timeframe) {
        try {
          console.log('🎯 Initializing weight goal tracking...');
          await WeightGoalTracker.initializeWeightGoal({
            currentWeight: data.weight,
            targetWeight: data.targetWeight,
            timeframe: data.timeframe,
            activityLevel: data.activityLevel || 'moderately_active'
          });
          console.log('✅ Weight goal tracking initialized successfully');
        } catch (weightError) {
          console.error('❌ Error initializing weight goal tracking:', weightError);
          // Don't fail onboarding if weight tracking fails
        }
      }

      // Mark onboarding as complete in state AFTER all async operations
      updateData('isComplete', true);
      console.log('✅ Onboarding marked as complete in state');

      // Add verification step
      const verification = await AsyncStorage.getItem('onboardingComplete');
      console.log('🔍 Verification - onboardingComplete in AsyncStorage:', verification);

    } catch (error) {
      console.error('❌ Error completing onboarding:', error);
      throw error; // Re-throw to let caller handle the error
    }
  };

  const getProgressPercentage = () => {
    return Math.round((data.currentStep / (totalSteps - 1)) * 100);
  };

  const canGoNext = () => {
    return data.currentStep < totalSteps - 1;
  };

  const canGoPrevious = () => {
    return data.currentStep > 0;
  };

  const contextValue: OnboardingContextType = {
    data,
    updateData,
    updateMultipleFields,
    nextStep,
    previousStep,
    goToStep,
    saveProgress,
    loadProgress,
    completeOnboarding,
    getProgressPercentage,
    canGoNext,
    canGoPrevious,
  };

  return (
    <OnboardingContext.Provider value={contextValue}>
      {children}
    </OnboardingContext.Provider>
  );
};

export const useOnboarding = () => {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
};
